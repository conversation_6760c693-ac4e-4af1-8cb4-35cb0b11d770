# Complete uv + pyproject.toml Flow - Step by Step
1. Project Setup: 
    - uv = Modern Python package manager (faster than pip). Speed: 10-100x faster package installation
    - Purpose: Manages packages and virtual environments automatically. More reliable than pip
    - Replaces: pip + venv workflow
2. curl -LsSf https://astral.sh/uv/install.sh | sh
    - curl = download files from the internet
    - L = follow redirects, Follow redirects (if the URL redirects to another URL)
    - s = silent mode (don't show progress bars)
    - S = show errors even in silent mode
    - f = fail silently
    - https://astral.sh/uv/install.sh = url to download
    - | = pipe
    - sh = run the downloaded file
** note before creating env do a git clone for the code 
3. uv sync
    - uv sync = install packages and create virtual environment
    - uv sync = same as pip install -r requirements.txt
4. Configuration File
    - pyproject.toml = Blueprint for the project
    - TOML = "Tom's Obvious Minimal Language" (config file format)
    - Contains: What packages you need,optional packages, project info, tool settings
    - **Better than YAML:**- No identation.Simple syntax. Official pythin standard pep 518. In YAML, one problem breaks everything. 

5. Dependencies
    - dependencies = [] = Must-have packages to run your code
    - optional-dependencies.dev = [] = Extra packages only for development
    - Example: You need requests to run, but pytest only for testing

6. Virtual Environment
    - uv sync reads pyproject.toml
    - Creates: .venv/ folder with all packages installed
    - Result: Isolated environment for your project
    - touch: create .env file: creates an empty file 


7. Code Quality Tools
    - mypy = Finds type errors before code runs.
        - example: def add_numbers(a: int, b: int) -> int: return a + b.  add_numbers("5", "10") will throw an error
    - ruff = Fixes formatting, finds unused imports, sorts code
    - example: ruff check src/ --fix
        - ruff check src/ = check all files in src/ folder
        - --fix = fix all formatting issues
    - Example: Catches add_numbers("5", "10") when expecting integers

8. Package Distribution
    - setuptools = Tool that packages your code for sharing
    - wheel (.whl) = Zip file format for Python packages
    - build_meta = The engine inside setuptools that does the work

9. Package Naming
    - Format: name-version-python-abi-platform.whl
    - Example: myproject-1.0.0-py3-none-any.whl
    - py3 = Python 3, none = no compiled code, any = works everywhere

10. Making Your Code Importable
    - packages = ["your_project"] = Makes from your_project import X work
    - package-dir = Points to where your source code lives
    - py.typed = Tells other tools "this package has type hints"

11. Tool Configuration
    - [tool.ruff] = Settings for code formatter/linter
    - lint.select = Which rules to enforce (spacing, imports, docs)
    - lint.ignore = Which rules to skip

12. Test File Exceptions
    - "tests/*" = Special rules for test files
    - Skip docs = Test files don't need documentation
    - Allow older syntax = Tests can use simpler code

13. The Complete Flow
    - Write pyproject.toml with dependencies
    - Run uv sync → creates .venv/ and installs packages
    - Activate environment → source .venv/bin/activate
    - Write code in src/your_project/
    - Run ruff and mypy to clean code
    - Build package → creates .whl file for distribution


Other Periphieral Notes: 
1. on mac .env files are hidden - to see them use ls - la in terminal or Cmd + Shift + . to toggle showing hidden files
2. uv uses pyproject.toml instead of requirements.txt
3. .egg-info/ - Project Metadata:
    - Purpose: Tells Python "this folder IS a package that can be imported"
    - Contains: Information ABOUT your local project (not external packages)
4. uv installs YOUR project (src/deep_research_from_scratch/) as a package
5. Set up tools: A tool that converts your Python code into a .whl file (a zip file with your code)
    - wheel: The format for that zip file
    - build: The tool that creates the .whl file
    - build_meta: Contains the actual code that converts your Python files into a distributable package. It peforms the below steps: 
        - Read your pyproject.toml
        - Find all .py files in src/
        - Copy them into a temporary folder
        - Add metadata (name, version, dependencies)
        - Create a .whl file
        - The .whl file is your package [ returns the .whl file]
    - .dist-info/: Metadata about your package (version, author, etc.)
    - Why you need this:
        - To share your code with others
        - To publish your code to PyPI
        - To use your code in other projects
6. Other ruff lint used: 
    - "tests/*" = ["D", "UP"] - skip documentation for test files
    - "D" = pydocstyle, check if codes have documentation
    - "UP" = pyupgrade, check if codes have outdated syntax
    - [tool.ruff.lint.pydocstyle] = convention = "google" - use google docstring format
7. One key difference between pydantic & Type hints [ List, Dict from import]: 
    - pydantic: from pydantic import BaseModel, Field
    - Type hints: from typing import List, Dict
    - pydantic is used for data validation and settings management. It allows you to define data models with type hints and additional validation logic. Pydantic is particularly useful when working with complex data structures, APIs, and configuration files.
    **- just a hint - Python doesn't enforce it whereas pydantic does.**
8. Rich python: 
    - Rich is a Python library for rich text and beautiful formatting in the terminal.
    - It provides a set of classes and functions for creating and styling text, tables, and other output.
    - It's used for pretty-printing and formatting output in the terminal.
    - Example: from rich import print; print("Hello, [bold red]World![/bold red]")
9. <font color='red'>__Agent Building Understanding in this project__</font>: 
    - 1. We first start to define scope i.e. what is needed, then do a research, then write a report. 
    - 2. to do this, first step is : to define a **STATE** where state is shared memory between agent layers/phases. example if we are working on a healthcare project to find impact of AI on healthcare, this is our scope, the research when initiates calls this state to understand the scope and then does the research and then updates the state with the research findings and then the write phase uses this state to write the report.   
    - 3. %%writefile is a jupyter magic command to write the contents of the cell to a file. 
        - example: %%writefile src/deep_research_from_scratch/state_scope.py
        - then we can import this file in any other notebook or .py file using from src.deep_research_from_scratch.state_scope import *
    - 4.from langchain_core.messages import BaseMessage:
        - Purpose: this supposed to standardize how conversations are represented in LangChain
        - Consistent Message Format: Ensures all messages have a consistent structure (role, content, etc.) example message.content, message.type, message.role etc. 
        - Support Different Message Types: 
            HumanMessage(content="What is AI?")           # User input
            AIMessage(content="AI is...")                # LLM response  
            SystemMessage(content="You are helpful...")  # System instructions
        ToolMessage(content="Search results...")     # Tool outputs
    - 5. from langgraph.graph import MessagesState: 
        - Purpose: this is a container that manages the state of a conversation. 
        - It is a dictionary that contains all the messages in the conversation. 
        - Each agent reads from state["messages"] and adds new messages to it.
    - 6. from langgraph.graph import add_messages: 
        - Reducer function that handles how new messages are added to state 
        - Smart merging: Doesn't just overwrite, intelligently combines messages
        - Handles: Deduplication, ordering, message updates
    -7. Till here how first notebook is working. 
        - we define the scope. we define state explained above. 
        - then we call helper functions from langraph to get the user question and what response we need which is defined by class AgentInputState(MessagesState) & then this question is passed to class AgentState(MessagesState) explained below. 
        - we also defined ClarifyWithUser(BaseModel): if we need more info from the user. 

    - 8. class AgentState(MessagesState):
        - Purpose: this is a custom state that extends the MessagesState class. 
        - research_brief: The focused research question generated from user input
        - supervisor_messages: Internal agent-to-agent communication
        - raw_notes: Unprocessed research findings
        - notes: Cleaned/processed research findings
        - final_report: The final output
    - 9. Langraph core components:
        - Nodes = Individual workers that perform specific tasks. Each node is a function that receives state, does work, and returns updated state.
        - State = Shared memory that flows between nodes. Contains all data accumulated during the workflow (messages, research findings, reports, etc.).
        - Control Flow = Decision logic that determines which node runs next based on current state. Can be:
        - Conditional edges: "If research is done, go to writing"
        - Router functions: Supervisor decides next step
        - Linear flow: Predetermined sequence
        - Command Flow : It's an alternative to conditional edges where you explicitly specify:
            - goto: Which node to go to next
            - update: State changes to make
            - Instead of returning state updates and letting conditional edges decide routing, Command does both operations together in one step.

        - Edges = Connections between nodes that define possible transitions.
        - Graph = The overall workflow structure connecting nodes with edges.
        - Execution flow: Start → Node processes state → Updates state → Control flow decides next node → Repeat until end condition.
        - from langgraph.graph import StateGraph, START, END: here graph as we discussed above is a graph or a flow whch connects all the edges and start and end are the starting and ending points of the graph.

    - 10. `Scoping.ipynb` next code: clarify_with_user
        - This is the first decision node that determines if the user provided enough info to start research or needs clarification. 
        - Takes current state in AgentState as input
        - Returns Command that goes to either "write_research_brief" node or END
        - structured_output_model = model.with_structured_output(ClarifyWithUser):forces the model to output in the format of ClarifyWithUser class. 
        - Sends user's conversation history to LLM. LLM decides if user request is clear enough. this is done in HumanMessage(content=clarify_with_user_instructions.format(
            messages=get_buffer_string(messages=state["messages"]), 
            date=get_today_str()
        )). # messages = state["messages"]
        - we are using get_today_str() to get the current date. 
    - 11. `Scoping.ipynb` next code: write_research_brief: 
        - how does get buffer string work? 
        - get_buffer_string is from langchain_core.messages transforms the messages readable conversation text that the LLM can process. 
        - example: Input: [HumanMessage("How does AI affect healthcare?"), AIMessage("I need more details...")]
        - Output:
        - Human: How does AI affect healthcare?
        - AI: I need more details...

    - 12. GRAPH CONSTRUCTION: 
        - deep_researcher_builder = StateGraph(AgentState, input_schema=AgentInputState): 
            - Creates empty graph that uses AgentState for internal state
            - Accepts AgentInputState as initial input
        - deep_researcher_builder.add_node("clarify_with_user", clarify_with_user): 
            - Adds clarify_with_user node to graph
            - Registers the functions as workflow steps
        - deep_researcher_builder.add_node("write_research_brief", write_research_brief): 
            - Adds write_research_brief node to graph
            - Registers the functions as workflow steps
        - Adding Edges: 
            - deep_researcher_builder.add_edge(START, "clarify_with_user")
            - deep_researcher_builder.add_edge("write_research_brief", END)
            this START → clarify_with_user (entry point)
            write_research_brief → END (exit point)
        - Compilation:
            - deep_researcher_builder.compile(): 
                - Compiles the graph into an executable workflow
                - Returns a function that can be called to run the workflow
     

